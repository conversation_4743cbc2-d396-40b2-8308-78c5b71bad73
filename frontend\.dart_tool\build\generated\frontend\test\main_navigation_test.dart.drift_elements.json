{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_test/flutter_test.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/main/main_navigation_screen.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/auth_providers.dart", "transitive": false}, {"uri": "package:frontend/data/models/user.dart", "transitive": false}], "elements": []}