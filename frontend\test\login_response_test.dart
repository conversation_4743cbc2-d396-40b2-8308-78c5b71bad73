import 'package:flutter_test/flutter_test.dart';
import 'package:frontend/data/models/user.dart';

void main() {
  group('LoginResponse Parsing Tests', () {
    test('should parse backend login response correctly', () {
      // This is the actual response structure from the backend
      final backendResponse = {
        "user": {
          "id": "e37e561b-1f77-4bcb-903f-2ca8159c1d48",
          "name": "System Administrator",
          "email": "<EMAIL>",
          "phone": "+919999999999",
          "role": "SUPER_ADMIN",
          "assignedProperties": [],
          "isActive": true,
          "avatar": null,
          "timezone": "Asia/Kolkata",
          "language": "en",
          "createdAt": "2025-06-04T06:55:08.012Z",
          "updatedAt": "2025-06-06T08:54:11.764Z",
          "lastLogin": "2025-06-06T09:06:06.192Z",
          "permissions": {
            "canViewDashboard": true,
            "canManageProperties": true,
            "canManageOffice": true,
            "canManageSecurity": true,
            "canManageMaintenance": true,
            "canManageUsers": true,
            "canViewReports": true,
            "canExportData": true,
            "allowedScreens": [
              "dashboard",
              "properties",
              "office_management",
              "security",
              "maintenance",
              "users",
              "reports",
              "settings"
            ],
            "allowedActions": [
              "create",
              "read",
              "update",
              "delete",
              "export",
              "import"
            ]
          }
        },
        "token": {
          "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************.aFTOmqyVl4Y_2qzmxenEJXOeWE1Tt9XOIR8cp6dZHTM",
          "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************.uD6GWja76UgR2X15FJT_e2UsZVkmdMjUO9qnMBh2x-8",
          "expiresAt": "2025-06-06T09:21:06.192Z",
          "tokenType": "Bearer"
        },
        "message": "Login successful"
      };

      // Test parsing
      expect(() => LoginResponse.fromJson(backendResponse), returnsNormally);
      
      final loginResponse = LoginResponse.fromJson(backendResponse);
      
      // Verify user data
      expect(loginResponse.user.id, equals("e37e561b-1f77-4bcb-903f-2ca8159c1d48"));
      expect(loginResponse.user.name, equals("System Administrator"));
      expect(loginResponse.user.email, equals("<EMAIL>"));
      expect(loginResponse.user.role, equals("SUPER_ADMIN"));
      
      // Verify token data
      expect(loginResponse.token.accessToken, isNotEmpty);
      expect(loginResponse.token.refreshToken, isNotEmpty);
      expect(loginResponse.token.tokenType, equals("Bearer"));
      expect(loginResponse.token.expiresAt, isA<DateTime>());
      
      // Verify message
      expect(loginResponse.message, equals("Login successful"));
      
      // Verify permissions
      expect(loginResponse.user.permissions.canViewDashboard, isTrue);
      expect(loginResponse.user.permissions.allowedScreens, contains("dashboard"));
      expect(loginResponse.user.permissions.allowedActions, contains("create"));
    });

    test('should handle AuthToken date parsing correctly', () {
      final tokenJson = {
        "accessToken": "test_access_token",
        "refreshToken": "test_refresh_token",
        "expiresAt": "2025-06-06T09:21:06.192Z",
        "tokenType": "Bearer"
      };

      expect(() => AuthToken.fromJson(tokenJson), returnsNormally);
      
      final authToken = AuthToken.fromJson(tokenJson);
      
      expect(authToken.accessToken, equals("test_access_token"));
      expect(authToken.refreshToken, equals("test_refresh_token"));
      expect(authToken.tokenType, equals("Bearer"));
      expect(authToken.expiresAt, isA<DateTime>());
      expect(authToken.expiresAt.toIso8601String(), equals("2025-06-06T09:21:06.192Z"));
    });
  });
}
