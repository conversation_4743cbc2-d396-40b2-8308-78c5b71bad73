{"valid_import": true, "imports": [{"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/data/models/user.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/auth_repository.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}, {"uri": "package:frontend/core/services/auth_service.dart", "transitive": false}], "elements": []}