# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\workspaces\\nsl\\back\\srsrmain\\frontend" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\flutter"
  "PROJECT_DIR=D:\\workspaces\\nsl\\back\\srsrmain\\frontend"
  "FLUTTER_ROOT=C:\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\workspaces\\nsl\\back\\srsrmain\\frontend\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\workspaces\\nsl\\back\\srsrmain\\frontend"
  "FLUTTER_TARGET=D:\\workspaces\\nsl\\back\\srsrmain\\frontend\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YjI1MzA1YTg4Mw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTQyNWU1ZTllYw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\workspaces\\nsl\\back\\srsrmain\\frontend\\.dart_tool\\package_config.json"
)
