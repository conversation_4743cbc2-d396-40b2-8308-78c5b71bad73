import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/presentation/screens/main/main_navigation_screen.dart';
import 'package:frontend/presentation/providers/auth_providers.dart';
import 'package:frontend/data/models/user.dart' as UserModel;

void main() {
  group('MainNavigationScreen Tests', () {
    testWidgets('should build without provider errors', (WidgetTester tester) async {
      // Create a mock user
      final mockUser = UserModel.User(
        id: 'test-id',
        name: 'Test User',
        email: '<EMAIL>',
        phone: '+**********',
        role: 'SUPER_ADMIN',
        assignedProperties: [],
        assignedPropertyIds: [],
        assignedOfficeIds: [],
        isActive: true,
        timezone: 'UTC',
        language: 'en',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        permissions: UserModel.UserPermissions(
          canViewDashboard: true,
          canManageProperties: true,
          canManageOffice: true,
          canManageSecurity: true,
          canManageMaintenance: true,
          canManageUsers: true,
          canViewReports: true,
          canExportData: true,
          canCreateMaintenance: true,
          canManageSecuritySystems: true,
          canManageWaterSystems: true,
          canManageElectricitySystems: true,
          allowedScreens: ['dashboard', 'properties', 'office_management', 'security', 'maintenance', 'users', 'reports', 'settings'],
          allowedActions: ['create', 'read', 'update', 'delete', 'export', 'import'],
        ),
      );

      // Create a provider override for testing
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentUserProvider.overrideWith((ref) => Future.value(mockUser)),
          ],
          child: MaterialApp(
            home: MainNavigationScreen(
              child: Container(
                child: Text('Test Child'),
              ),
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the widget builds without errors
      expect(find.byType(MainNavigationScreen), findsOneWidget);
      expect(find.byType(BottomNavigationBar), findsOneWidget);
      expect(find.text('Test Child'), findsOneWidget);
    });

    testWidgets('should show admin panel for super admin', (WidgetTester tester) async {
      // Create a super admin user
      final superAdminUser = UserModel.User(
        id: 'admin-id',
        name: 'Super Admin',
        email: '<EMAIL>',
        phone: '+**********',
        role: 'SUPER_ADMIN',
        assignedProperties: [],
        assignedPropertyIds: [],
        assignedOfficeIds: [],
        isActive: true,
        timezone: 'UTC',
        language: 'en',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        permissions: UserModel.UserPermissions(
          canViewDashboard: true,
          canManageProperties: true,
          canManageOffice: true,
          canManageSecurity: true,
          canManageMaintenance: true,
          canManageUsers: true,
          canViewReports: true,
          canExportData: true,
          canCreateMaintenance: true,
          canManageSecuritySystems: true,
          canManageWaterSystems: true,
          canManageElectricitySystems: true,
          allowedScreens: ['dashboard', 'properties', 'office_management', 'security', 'maintenance', 'users', 'reports', 'settings'],
          allowedActions: ['create', 'read', 'update', 'delete', 'export', 'import'],
        ),
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentUserProvider.overrideWith((ref) => Future.value(superAdminUser)),
          ],
          child: MaterialApp(
            home: MainNavigationScreen(
              child: Container(
                child: Text('Test Child'),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that admin panel is available for super admin
      expect(find.byType(BottomNavigationBar), findsOneWidget);
      
      // Check that we have the expected number of navigation items (including admin)
      final bottomNavBar = tester.widget<BottomNavigationBar>(find.byType(BottomNavigationBar));
      expect(bottomNavBar.items.length, equals(5)); // Dashboard, Properties, Office, Admin, Settings
    });

    testWidgets('should not show admin panel for regular user', (WidgetTester tester) async {
      // Create a regular user
      final regularUser = UserModel.User(
        id: 'user-id',
        name: 'Regular User',
        email: '<EMAIL>',
        phone: '+**********',
        role: 'PROPERTY_MANAGER',
        assignedProperties: [],
        assignedPropertyIds: [],
        assignedOfficeIds: [],
        isActive: true,
        timezone: 'UTC',
        language: 'en',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        permissions: UserModel.UserPermissions(
          canViewDashboard: true,
          canManageProperties: true,
          canManageOffice: false,
          canManageSecurity: true,
          canManageMaintenance: true,
          canManageUsers: false,
          canViewReports: true,
          canExportData: true,
          canCreateMaintenance: true,
          canManageSecuritySystems: true,
          canManageWaterSystems: true,
          canManageElectricitySystems: true,
          allowedScreens: ['dashboard', 'properties', 'maintenance', 'security', 'reports'],
          allowedActions: ['create', 'read', 'update', 'export'],
        ),
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentUserProvider.overrideWith((ref) => Future.value(regularUser)),
          ],
          child: MaterialApp(
            home: MainNavigationScreen(
              child: Container(
                child: Text('Test Child'),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that admin panel is NOT available for regular user
      final bottomNavBar = tester.widget<BottomNavigationBar>(find.byType(BottomNavigationBar));
      expect(bottomNavBar.items.length, equals(4)); // Dashboard, Properties, Office, Settings (no Admin)
    });
  });
}
